import * as PIXI from 'pixi.js';
import type { CompanionConfig, CompanionBehavior, CompanionType } from '../types';

/**
 * Base companion class that handles common companion behavior
 */
export abstract class BaseCompanion implements CompanionBehavior {
    protected sprite: PIXI.AnimatedSprite;
    protected app: PIXI.Application;
    protected config: CompanionConfig;
    protected position: { x: number; y: number };
    protected targetPosition: { x: number; y: number };
    protected velocity: { x: number; y: number };
    protected currentAnimation: 'idle' | 'walking' | 'running';
    protected frames: { idle: PIXI.Texture[]; walking: PIXI.Texture[] };
    protected isInitialized: boolean = false;

    constructor(app: PIXI.Application, config: CompanionConfig) {
        this.app = app;
        this.config = config;
        this.position = { x: 0, y: 0 };
        this.targetPosition = { x: 0, y: 0 };
        this.velocity = { x: 0, y: 0 };
        this.currentAnimation = 'idle';
        this.frames = { idle: [], walking: [] };
        this.sprite = new PIXI.AnimatedSprite([PIXI.Texture.WHITE]);
    }

    /**
     * Initialize the companion with sprite loading
     */
    async initialize(): Promise<void> {
        try {
            await this.loadSprites();
            this.setupSprite();
            this.isInitialized = true;
        } catch (error) {
            console.error(`Failed to initialize ${this.config.type} companion:`, error);
            this.createFallbackSprite();
            this.isInitialized = true;
        }
    }

    /**
     * Load sprite sheets and extract frames
     */
    protected abstract loadSprites(): Promise<void>;

    /**
     * Setup the sprite properties
     */
    protected setupSprite(): void {
        this.sprite.anchor.set(0.5, 1.0); // Bottom-center anchor
        this.sprite.scale.set(this.config.scale, this.config.scale);
        this.sprite.animationSpeed = this.config.animationSpeed;
        this.sprite.loop = true;
        this.sprite.zIndex = 90; // Render behind monkey but in front of world
        this.app.stage.addChild(this.sprite);
        this.setAnimation('idle');
    }

    /**
     * Create a fallback sprite if loading fails
     */
    protected createFallbackSprite(): void {
        this.sprite = new PIXI.AnimatedSprite([PIXI.Texture.WHITE]);
        this.sprite.width = this.config.frameSize.width * this.config.scale;
        this.sprite.height = this.config.frameSize.height * this.config.scale;
        this.sprite.tint = 0x8B4513; // Brown color
        this.sprite.anchor.set(0.5, 1.0);
        this.sprite.zIndex = 90;
        this.app.stage.addChild(this.sprite);
    }

    /**
     * Update companion behavior each frame
     */
    update(deltaTime: number, targetPosition: { x: number; y: number }): void {
        if (!this.isInitialized) return;

        this.targetPosition = targetPosition;
        this.updateMovement(deltaTime);
        this.updateAnimation();
        this.updateSpritePosition();
    }

    /**
     * Update movement towards target
     */
    protected updateMovement(deltaTime: number): void {
        const dx = this.targetPosition.x - this.position.x;
        const dy = this.targetPosition.y - this.position.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        // Only move if we're far enough from the target
        if (distance > this.config.followDistance) {
            // Calculate direction
            const dirX = dx / distance;
            const dirY = dy / distance;

            // Enhanced speed calculation with smooth acceleration
            const maxSpeed = this.config.moveSpeed;
            const acceleration = 0.3;
            const deceleration = 0.8;

            // Calculate desired velocity based on distance
            let desiredSpeed = Math.min(maxSpeed, distance * 0.15);

            // If we're very far, move faster to catch up
            if (distance > this.config.followDistance * 3) {
                desiredSpeed = maxSpeed * 1.5;
            }

            // Smooth velocity interpolation
            const targetVelX = dirX * desiredSpeed;
            const targetVelY = dirY * desiredSpeed;

            this.velocity.x += (targetVelX - this.velocity.x) * acceleration;
            this.velocity.y += (targetVelY - this.velocity.y) * acceleration;

            // Update position with collision detection
            const newX = this.position.x + this.velocity.x * deltaTime;
            const newY = this.position.y + this.velocity.y * deltaTime;

            // Simple boundary collision detection
            const screenWidth = this.app.screen.width;
            const spriteWidth = this.config.frameSize.width * this.config.scale;
            const spriteHeight = this.config.frameSize.height * this.config.scale;

            // Keep companion within screen bounds
            this.position.x = Math.max(spriteWidth / 2, Math.min(newX, screenWidth - spriteWidth / 2));
            this.position.y = Math.max(spriteHeight, Math.min(newY, this.app.screen.height));

            // Flip sprite based on movement direction with deadzone
            if (Math.abs(this.velocity.x) > 0.5) {
                if (this.velocity.x < 0) {
                    this.sprite.scale.x = -Math.abs(this.config.scale);
                } else {
                    this.sprite.scale.x = Math.abs(this.config.scale);
                }
            }
        } else {
            // Close enough to target, apply smooth deceleration
            this.velocity.x *= 0.85; // Smoother friction
            this.velocity.y *= 0.85;

            // Stop very small movements to prevent jittering
            if (Math.abs(this.velocity.x) < 0.1) this.velocity.x = 0;
            if (Math.abs(this.velocity.y) < 0.1) this.velocity.y = 0;
        }
    }

    /**
     * Update animation based on movement
     */
    protected updateAnimation(): void {
        const speed = Math.sqrt(this.velocity.x * this.velocity.x + this.velocity.y * this.velocity.y);

        if (speed > 1) {
            this.setAnimation('walking');
        } else {
            this.setAnimation('idle');
        }
    }

    /**
     * Update sprite position
     */
    protected updateSpritePosition(): void {
        this.sprite.x = this.position.x;
        this.sprite.y = this.position.y;
    }

    /**
     * Set animation state
     */
    setAnimation(state: 'idle' | 'walking' | 'running'): void {
        if (this.currentAnimation === state || !this.isInitialized) return;

        this.currentAnimation = state;

        // Remove current sprite and create new one with appropriate frames
        const currentPosition = { x: this.sprite.x, y: this.sprite.y };
        const currentScale = { x: this.sprite.scale.x, y: this.sprite.scale.y };

        this.app.stage.removeChild(this.sprite);

        let frames: PIXI.Texture[];
        switch (state) {
            case 'walking':
            case 'running':
                frames = this.frames.walking.length > 0 ? this.frames.walking : this.frames.idle;
                break;
            case 'idle':
            default:
                frames = this.frames.idle;
                break;
        }

        this.sprite = new PIXI.AnimatedSprite(frames);
        this.sprite.anchor.set(0.5, 1.0);
        this.sprite.scale.set(currentScale.x, currentScale.y);
        this.sprite.x = currentPosition.x;
        this.sprite.y = currentPosition.y;
        this.sprite.animationSpeed = this.config.animationSpeed;
        this.sprite.loop = true;
        this.sprite.zIndex = 90;
        this.sprite.play();
        this.app.stage.addChild(this.sprite);
    }

    /**
     * Get current position
     */
    getPosition(): { x: number; y: number } {
        return { ...this.position };
    }

    /**
     * Set position
     */
    setPosition(x: number, y: number): void {
        this.position.x = x;
        this.position.y = y;
        this.updateSpritePosition();
    }

    /**
     * Destroy the companion
     */
    destroy(): void {
        if (this.sprite && this.sprite.parent) {
            this.app.stage.removeChild(this.sprite);
        }
        this.isInitialized = false;
    }
}

/**
 * Companion Manager handles all companion instances
 */
export class CompanionManager {
    private companions: Map<CompanionType, BaseCompanion> = new Map();
    private activeCompanion: BaseCompanion | null = null;
    private app: PIXI.Application;

    constructor(app: PIXI.Application) {
        this.app = app;
    }

    /**
     * Add a companion to the manager
     */
    addCompanion(companion: BaseCompanion, type: CompanionType): void {
        this.companions.set(type, companion);
    }

    /**
     * Set the active companion
     */
    async setActiveCompanion(type: CompanionType): Promise<void> {
        // Deactivate current companion
        if (this.activeCompanion) {
            this.activeCompanion.destroy();
        }

        const companion = this.companions.get(type);
        if (companion) {
            await companion.initialize();
            this.activeCompanion = companion;
        }
    }

    /**
     * Update the active companion
     */
    update(deltaTime: number, targetPosition: { x: number; y: number }): void {
        if (this.activeCompanion) {
            this.activeCompanion.update(deltaTime, targetPosition);
        }
    }

    /**
     * Get the active companion
     */
    getActiveCompanion(): BaseCompanion | null {
        return this.activeCompanion;
    }

    /**
     * Remove all companions
     */
    clearAll(): void {
        this.companions.forEach(companion => companion.destroy());
        this.companions.clear();
        this.activeCompanion = null;
    }
}
