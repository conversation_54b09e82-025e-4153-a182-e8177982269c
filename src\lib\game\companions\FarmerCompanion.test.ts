import { describe, it, expect, beforeEach, vi } from 'vitest';
import { FarmerCompanion, createFarmerCompanion, FARMER_CONFIG } from './FarmerCompanion';

// Mock PIXI
vi.mock('pixi.js', () => ({
    Application: vi.fn(() => ({
        stage: {
            addChild: vi.fn(),
            removeChild: vi.fn(),
            sortableChildren: true
        },
        screen: {
            width: 800,
            height: 600
        }
    })),
    AnimatedSprite: vi.fn(() => ({
        anchor: { set: vi.fn() },
        scale: { set: vi.fn(), x: 1, y: 1 },
        x: 0,
        y: 0,
        animationSpeed: 0.1,
        loop: true,
        play: vi.fn(),
        stop: vi.fn(),
        zIndex: 90,
        parent: null
    })),
    Texture: vi.fn((source, frame) => ({
        source,
        frame
    })),
    Rectangle: vi.fn((x, y, width, height) => ({ x, y, width, height })),
    Assets: {
        load: vi.fn().mockResolvedValue({
            width: 768,
            height: 64,
            source: { width: 768, height: 64 }
        })
    }
}));

const mockApp = {
    stage: {
        addChild: vi.fn(),
        removeChild: vi.fn(),
        sortableChildren: true
    },
    screen: {
        width: 800,
        height: 600
    }
} as any;

describe('FarmerCompanion', () => {
    let farmerCompanion: FarmerCompanion;

    beforeEach(() => {
        vi.clearAllMocks();
        farmerCompanion = new FarmerCompanion(mockApp);
    });

    describe('configuration', () => {
        it('should have correct farmer configuration', () => {
            expect(FARMER_CONFIG.type).toBe('farmer');
            expect(FARMER_CONFIG.name).toBe('Farmer');
            expect(FARMER_CONFIG.spriteSheet).toBe('/assets/farmer/farmer-idle.png');
            expect(FARMER_CONFIG.frameCount).toBe(24);
            expect(FARMER_CONFIG.frameSize).toEqual({ width: 32, height: 32 });
            expect(FARMER_CONFIG.scale).toBe(4);
            expect(FARMER_CONFIG.animationSpeed).toBe(0.08);
            expect(FARMER_CONFIG.followDistance).toBe(80);
            expect(FARMER_CONFIG.moveSpeed).toBe(3);
        });

        it('should initialize with farmer config', () => {
            expect(farmerCompanion['config']).toEqual(FARMER_CONFIG);
        });
    });

    describe('sprite loading', () => {
        it('should load farmer sprites correctly', async () => {
            const mockTexture = {
                width: 768,
                height: 64,
                source: { width: 768, height: 64 }
            };

            vi.mocked(require('pixi.js').Assets.load).mockResolvedValue(mockTexture);

            await farmerCompanion.initialize();

            expect(require('pixi.js').Assets.load).toHaveBeenCalledWith('/assets/farmer/farmer-idle.png');
            expect(farmerCompanion['frames'].idle.length).toBe(24);
            expect(farmerCompanion['frames'].walking.length).toBe(8); // Every 3rd frame
        });

        it('should handle sprite loading errors gracefully', async () => {
            vi.mocked(require('pixi.js').Assets.load).mockRejectedValue(new Error('Load failed'));

            await farmerCompanion.initialize();

            // Should still be initialized with fallback sprite
            expect(farmerCompanion['isInitialized']).toBe(true);
            expect(mockApp.stage.addChild).toHaveBeenCalled();
        });
    });

    describe('movement behavior', () => {
        beforeEach(async () => {
            await farmerCompanion.initialize();
        });

        it('should move towards target when far enough', () => {
            const targetPosition = { x: 200, y: 200 };
            farmerCompanion.setPosition(0, 0);

            farmerCompanion.update(16, targetPosition);

            const position = farmerCompanion.getPosition();
            expect(position.x).toBeGreaterThan(0);
            expect(position.y).toBeGreaterThan(0);
        });

        it('should stop when close to target', () => {
            const targetPosition = { x: 50, y: 50 };
            farmerCompanion.setPosition(50, 50);

            farmerCompanion.update(16, targetPosition);

            const position = farmerCompanion.getPosition();
            expect(position.x).toBe(50);
            expect(position.y).toBe(50);
        });

        it('should respect follow distance', () => {
            const targetPosition = { x: 100, y: 100 };
            farmerCompanion.setPosition(100 - FARMER_CONFIG.followDistance + 10, 100);

            const initialPosition = farmerCompanion.getPosition();
            farmerCompanion.update(16, targetPosition);
            const finalPosition = farmerCompanion.getPosition();

            // Should not move much since within follow distance
            expect(Math.abs(finalPosition.x - initialPosition.x)).toBeLessThan(5);
        });
    });

    describe('animation behavior', () => {
        beforeEach(async () => {
            await farmerCompanion.initialize();
        });

        it('should use walking animation when moving fast enough', () => {
            const setAnimationSpy = vi.spyOn(farmerCompanion, 'setAnimation');

            // Set up movement that should trigger walking animation
            farmerCompanion.setPosition(0, 0);
            farmerCompanion['velocity'] = { x: 2, y: 0 }; // Fast enough to trigger walking

            farmerCompanion['updateAnimation']();

            expect(setAnimationSpy).toHaveBeenCalledWith('walking');
        });

        it('should use idle animation when moving slowly', () => {
            const setAnimationSpy = vi.spyOn(farmerCompanion, 'setAnimation');

            // Set up slow movement that should trigger idle animation
            farmerCompanion.setPosition(0, 0);
            farmerCompanion['velocity'] = { x: 0.3, y: 0 }; // Too slow for walking

            farmerCompanion['updateAnimation']();

            expect(setAnimationSpy).toHaveBeenCalledWith('idle');
        });

        it('should have different speed threshold than base companion', () => {
            // Farmer should switch to walking at speed > 0.5, not > 1
            const setAnimationSpy = vi.spyOn(farmerCompanion, 'setAnimation');

            farmerCompanion['velocity'] = { x: 0.7, y: 0 }; // Between 0.5 and 1
            farmerCompanion['updateAnimation']();

            expect(setAnimationSpy).toHaveBeenCalledWith('walking');
        });
    });

    describe('factory function', () => {
        it('should create farmer companion with correct app', () => {
            const farmer = createFarmerCompanion(mockApp);

            expect(farmer).toBeInstanceOf(FarmerCompanion);
            expect(farmer['app']).toBe(mockApp);
            expect(farmer['config']).toEqual(FARMER_CONFIG);
        });
    });

    describe('sprite frame extraction', () => {
        it('should extract correct number of frames from sprite sheet', async () => {
            const mockTexture = {
                width: 768, // 24 frames * 32 width = 768
                height: 64,
                source: { width: 768, height: 64 }
            };

            vi.mocked(require('pixi.js').Assets.load).mockResolvedValue(mockTexture);

            await farmerCompanion['loadSprites']();

            // Should extract 6 idle frames (facing forward)
            expect(farmerCompanion['frames'].idle.length).toBe(6);

            // Should extract 12 walking frames (facing right + left)
            expect(farmerCompanion['frames'].walking.length).toBe(12);

            // Should have directional frames
            expect(farmerCompanion['frames'].facingRight.length).toBe(6);
            expect(farmerCompanion['frames'].facingLeft.length).toBe(6);
            expect(farmerCompanion['frames'].facingBack.length).toBe(6);
            expect(farmerCompanion['frames'].facingForward.length).toBe(6);
        });

        it('should handle different sprite sheet layouts', async () => {
            const mockTexture = {
                width: 384, // 12 frames per row * 32 width = 384
                height: 128, // 2 rows * 64 height = 128
                source: { width: 384, height: 128 }
            };

            vi.mocked(require('pixi.js').Assets.load).mockResolvedValue(mockTexture);

            await farmerCompanion['loadSprites']();

            // Should still extract 24 frames total
            expect(farmerCompanion['frames'].idle.length).toBe(24);
        });
    });

    describe('positioning and scaling', () => {
        beforeEach(async () => {
            await farmerCompanion.initialize();
        });

        it('should position sprite correctly', () => {
            farmerCompanion.setPosition(100, 200);

            expect(farmerCompanion['sprite'].x).toBe(100);
            expect(farmerCompanion['sprite'].y).toBe(200);
        });

        it('should scale sprite to 4x', () => {
            expect(farmerCompanion['sprite'].scale.x).toBe(4);
            expect(farmerCompanion['sprite'].scale.y).toBe(4);
        });

        it('should use bottom-center anchor', () => {
            expect(farmerCompanion['sprite'].anchor.set).toHaveBeenCalledWith(0.5, 1.0);
        });
    });
});
