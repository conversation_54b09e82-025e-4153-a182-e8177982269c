import * as PIXI from 'pixi.js';
import { BaseCompanion } from '../CompanionManager';
import type { CompanionConfig } from '../../types';

/**
 * Farmer companion configuration
 */
export const FARMER_CONFIG: CompanionConfig = {
    type: 'farmer',
    name: 'Farmer',
    spriteSheet: '/assets/farmer/farmer-idle.png',
    frameCount: 24,
    frameSize: { width: 32, height: 32 },
    scale: 4,
    animationSpeed: 0.08, // Slower animation for more natural idle movement
    followDistance: 80,
    moveSpeed: 3
};

/**
 * Farmer walking sprite configuration
 */
export const FARMER_WALKING_CONFIG = {
    spriteSheet: '/assets/farmer/farmer-walking.png',
    frameCount: 24,
    frameSize: { width: 32, height: 32 }
};

/**
 * Extended frames interface for directional animations
 */
interface FarmerFrames {
    idle: PIXI.Texture[];
    walking: PIXI.Texture[];
    // Idle frames by direction
    idleFacingRight: PIXI.Texture[];
    idleFacingLeft: PIXI.Texture[];
    idleFacingBack: PIXI.Texture[];
    idleFacingForward: PIXI.Texture[];
    // Walking frames by direction
    walkingFacingRight: PIXI.Texture[];
    walkingFacingLeft: PIXI.Texture[];
    walkingFacingBack: PIXI.Texture[];
    walkingFacingForward: PIXI.Texture[];
}

/**
 * Farmer companion class
 */
export class FarmerCompanion extends BaseCompanion {
    protected declare frames: FarmerFrames;

    // Physics properties
    private verticalVelocity: number = 0;
    private isOnGround: boolean = false;
    private readonly GRAVITY = 0.5; // Same as monkey
    private readonly JUMP_FORCE = -12; // Same as monkey
    private readonly GROUND_Y = 500; // Same as monkey
    private worldBuilder: any = null; // Will be set from GameCanvas

    constructor(app: PIXI.Application) {
        super(app, FARMER_CONFIG);
        // Initialize extended frames structure
        this.frames = {
            idle: [],
            walking: [],
            // Idle frames by direction
            idleFacingRight: [],
            idleFacingLeft: [],
            idleFacingBack: [],
            idleFacingForward: [],
            // Walking frames by direction
            walkingFacingRight: [],
            walkingFacingLeft: [],
            walkingFacingBack: [],
            walkingFacingForward: []
        };
    }

    /**
     * Set world builder for collision detection
     */
    setWorldBuilder(worldBuilder: any): void {
        this.worldBuilder = worldBuilder;
    }

    /**
     * Load farmer sprite sheets and extract frames
     */
    protected async loadSprites(): Promise<void> {
        try {
            // Load both idle and walking sprite sheets
            const [idleTexture, walkingTexture] = await Promise.all([
                PIXI.Assets.load(this.config.spriteSheet),
                PIXI.Assets.load(FARMER_WALKING_CONFIG.spriteSheet)
            ]);

            // Extract frames from both sprite sheets
            // Both sprite sheets are 32x32 pixels per frame, 24 frames total
            const frameWidth = this.config.frameSize.width;
            const frameHeight = this.config.frameSize.height;
            const totalFrames = this.config.frameCount;

            // Helper function to extract frames from a texture
            const extractFrames = (texture: PIXI.Texture): PIXI.Texture[] => {
                const textureWidth = texture.width;
                const framesPerRow = Math.floor(textureWidth / frameWidth);
                const frames: PIXI.Texture[] = [];

                for (let i = 0; i < totalFrames; i++) {
                    const col = i % framesPerRow;
                    const row = Math.floor(i / framesPerRow);

                    const frame = new PIXI.Rectangle(
                        col * frameWidth,
                        row * frameHeight,
                        frameWidth,
                        frameHeight
                    );

                    const frameTexture = new PIXI.Texture({ source: texture.source, frame });
                    frames.push(frameTexture);
                }

                return frames;
            };

            // Extract frames from both textures
            const idleFrames = extractFrames(idleTexture);
            const walkingFrames = extractFrames(walkingTexture);

            // Organize idle frames by direction (6 frames each)
            // Frame layout: [0-5] facing right, [6-11] facing back, [12-17] facing left, [18-23] facing forward
            this.frames.idleFacingRight = idleFrames.slice(0, 6);    // Frames 0-5
            this.frames.idleFacingBack = idleFrames.slice(6, 12);     // Frames 6-11
            this.frames.idleFacingLeft = idleFrames.slice(12, 18);    // Frames 12-17
            this.frames.idleFacingForward = idleFrames.slice(18, 24); // Frames 18-23

            // Organize walking frames by direction (6 frames each)
            this.frames.walkingFacingRight = walkingFrames.slice(0, 6);    // Frames 0-5
            this.frames.walkingFacingBack = walkingFrames.slice(6, 12);     // Frames 6-11
            this.frames.walkingFacingLeft = walkingFrames.slice(12, 18);    // Frames 12-17
            this.frames.walkingFacingForward = walkingFrames.slice(18, 24); // Frames 18-23

            // Set default idle and walking frames (forward-facing)
            this.frames.idle = this.frames.idleFacingForward;
            this.frames.walking = this.frames.walkingFacingForward;

            console.log(`Farmer companion loaded: ${this.frames.idle.length} idle frames, ${this.frames.walking.length} walking frames, 6 frames per direction for each animation`);

        } catch (error) {
            console.error('Failed to load farmer sprites:', error);
            throw error;
        }
    }

    /**
     * Override setupSprite to use farmer-specific initialization
     */
    protected setupSprite(): void {
        console.log('FarmerCompanion.setupSprite() called');
        console.log('Current stage children count:', this.app.stage.children.length);

        // Remove any existing sprite first (from base class constructor)
        if (this.sprite && this.sprite.parent) {
            console.log('Removing existing sprite from stage');
            this.app.stage.removeChild(this.sprite);
        }

        // Create initial sprite with idle frames
        console.log('Creating new farmer sprite with', this.frames.idleFacingForward.length, 'frames');
        this.sprite = new PIXI.AnimatedSprite(this.frames.idleFacingForward);
        this.sprite.anchor.set(0.5, 1.0); // Bottom-center anchor
        this.sprite.scale.set(this.config.scale, this.config.scale);
        this.sprite.animationSpeed = this.config.animationSpeed;
        this.sprite.loop = true;
        this.sprite.zIndex = 90; // Render behind monkey but in front of world
        this.sprite.play();
        this.app.stage.addChild(this.sprite);

        console.log('Added farmer sprite to stage. New children count:', this.app.stage.children.length);

        // Set initial animation state without calling setAnimation
        this.currentAnimation = 'idle';

        // Initialize position on ground
        this.position.y = this.GROUND_Y;
        this.isOnGround = true;
        this.verticalVelocity = 0;

        // Count farmer sprites on stage for debugging
        const farmerSprites = this.app.stage.children.filter(child =>
            child instanceof PIXI.AnimatedSprite && child.zIndex === 90
        );
        console.log('FarmerCompanion sprite setup complete. Total farmer-like sprites on stage:', farmerSprites.length);

        if (farmerSprites.length > 1) {
            console.warn('WARNING: Multiple farmer sprites detected!', farmerSprites);
        }
    }

    /**
     * Farmer-specific behavior updates with physics
     */
    update(deltaTime: number, targetPosition: { x: number; y: number }): void {
        if (!this.isInitialized) return;

        // Only use target X position, let physics handle Y
        this.targetPosition.x = targetPosition.x;
        // Keep current Y as target for jump detection, but don't override physics
        this.targetPosition.y = targetPosition.y;

        // Apply physics first
        this.updatePhysics(deltaTime);

        // Then update horizontal movement only
        this.updateHorizontalMovement(deltaTime);

        // Update animation based on movement
        this.updateAnimation();

        // Update sprite position
        this.updateSpritePosition();
    }

    /**
     * Update physics (gravity, ground collision, jumping)
     */
    private updatePhysics(deltaTime: number): void {
        const farmerWidth = this.config.frameSize.width * this.config.scale;
        const farmerHeight = this.config.frameSize.height * this.config.scale;

        // Check if farmer is on ground or on a platform
        // Farmer uses bottom-center anchor (0.5, 1.0), so position.y is bottom edge
        let isOnGround = this.position.y >= this.GROUND_Y;

        // Check if standing on a platform
        if (!isOnGround && this.worldBuilder) {
            const tilesBelow = this.worldBuilder.getTilesInArea(
                this.position.x - farmerWidth / 2, // Left edge
                this.position.y - 5, // Check just below farmer's feet
                farmerWidth,
                10 // Small area below feet
            );

            for (const tile of tilesBelow) {
                if (tile.sprite) {
                    const tileTop = tile.sprite.y;
                    const tileLeft = tile.sprite.x;
                    const tileRight = tile.sprite.x + tile.sprite.width;

                    const farmerBottom = this.position.y; // Bottom edge (anchor is bottom-center)
                    const farmerLeft = this.position.x - farmerWidth / 2; // Left edge
                    const farmerRight = this.position.x + farmerWidth / 2; // Right edge

                    // Check if farmer is standing on this tile
                    const horizontalOverlap = farmerRight > tileLeft && farmerLeft < tileRight;
                    const verticalAlignment = Math.abs(farmerBottom - tileTop) < 5; // Small tolerance

                    if (horizontalOverlap && verticalAlignment) {
                        isOnGround = true;
                        break;
                    }
                }
            }
        }

        this.isOnGround = isOnGround;

        // Apply gravity if not on ground
        if (!this.isOnGround) {
            this.verticalVelocity += this.GRAVITY * deltaTime;
        } else {
            // Reset vertical velocity when on ground
            if (this.verticalVelocity > 0) {
                this.verticalVelocity = 0;
            }
        }

        // Update vertical position
        this.position.y += this.verticalVelocity * deltaTime;

        // Platform collision detection (when falling)
        if (this.verticalVelocity > 0 && this.worldBuilder) {
            const tilesInArea = this.worldBuilder.getTilesInArea(
                this.position.x - farmerWidth / 2, // Left edge
                this.position.y - farmerHeight, // Top edge (since anchor is bottom-center)
                farmerWidth,
                farmerHeight
            );

            for (const tile of tilesInArea) {
                if (tile.sprite) {
                    const tileTop = tile.sprite.y;
                    const tileLeft = tile.sprite.x;
                    const tileRight = tile.sprite.x + tile.sprite.width;

                    const farmerBottom = this.position.y; // Bottom edge
                    const farmerLeft = this.position.x - farmerWidth / 2; // Left edge
                    const farmerRight = this.position.x + farmerWidth / 2; // Right edge

                    // Horizontal overlap check
                    const horizontalOverlap = farmerRight > tileLeft && farmerLeft < tileRight;

                    // Vertical collision check (landing on top)
                    if (horizontalOverlap &&
                        farmerBottom >= tileTop &&
                        farmerBottom <= tileTop + 10 && // Small tolerance for landing
                        this.position.y - farmerHeight < tileTop) { // Farmer's top is above tile's top

                        this.position.y = tileTop; // Position bottom at tile top
                        this.verticalVelocity = 0;
                        this.isOnGround = true;
                        break; // Stop checking once we find a collision
                    }
                }
            }
        }

        // Ground collision
        if (this.position.y >= this.GROUND_Y) {
            this.position.y = this.GROUND_Y;
            this.verticalVelocity = 0;
            this.isOnGround = true;
        }

        // Check if we need to jump to reach the target
        this.checkJumpToTarget();
    }

    /**
     * Check if farmer needs to jump to reach the target
     */
    private checkJumpToTarget(): void {
        if (!this.isOnGround) return; // Can only jump when on ground

        const targetHeight = this.targetPosition.y;
        const currentHeight = this.position.y;
        const heightDifference = currentHeight - targetHeight; // Positive if target is higher

        // If target is significantly higher and we're close horizontally, jump
        const horizontalDistance = Math.abs(this.targetPosition.x - this.position.x);
        const JUMP_THRESHOLD = 30; // Height difference to trigger jump
        const HORIZONTAL_THRESHOLD = 100; // Must be close horizontally to jump

        if (heightDifference > JUMP_THRESHOLD && horizontalDistance < HORIZONTAL_THRESHOLD) {
            this.verticalVelocity = this.JUMP_FORCE;
            this.isOnGround = false;
        }
    }

    /**
     * Update horizontal movement only (vertical handled by physics)
     */
    private updateHorizontalMovement(deltaTime: number): void {
        const dx = this.targetPosition.x - this.position.x;
        const horizontalDistance = Math.abs(dx);

        // Only move horizontally if we're far enough from the target
        if (horizontalDistance > this.config.followDistance) {
            // Calculate horizontal direction
            const dirX = dx / horizontalDistance;

            // Enhanced speed calculation with smooth acceleration
            const maxSpeed = this.config.moveSpeed;
            const acceleration = 0.3;

            // Calculate desired velocity based on distance
            let desiredSpeed = Math.min(maxSpeed, horizontalDistance * 0.15);

            // If we're very far, move faster to catch up
            if (horizontalDistance > this.config.followDistance * 3) {
                desiredSpeed = maxSpeed * 1.5;
            }

            // Smooth velocity interpolation (horizontal only)
            const targetVelX = dirX * desiredSpeed;
            this.velocity.x += (targetVelX - this.velocity.x) * acceleration;

            // Update horizontal position with collision avoidance
            let newX = this.position.x + this.velocity.x * deltaTime;

            // Add collision avoidance with player (monkey) - horizontal only
            const MIN_SEPARATION = 40; // Minimum distance to maintain from player
            const playerDistance = Math.abs(newX - this.targetPosition.x);

            if (playerDistance < MIN_SEPARATION) {
                // Apply horizontal offset to avoid stacking
                const offsetDirection = newX < this.targetPosition.x ? -1 : 1;
                newX = this.targetPosition.x + (offsetDirection * MIN_SEPARATION);

                // Reduce velocity when avoiding collision
                this.velocity.x *= 0.5;
            }

            // Simple boundary collision detection (horizontal only)
            const screenWidth = this.app.screen.width;
            const spriteWidth = this.config.frameSize.width * this.config.scale;

            // Keep companion within screen bounds
            this.position.x = Math.max(spriteWidth / 2, Math.min(newX, screenWidth - spriteWidth / 2));

            // Flip sprite based on movement direction with deadzone
            if (Math.abs(this.velocity.x) > 0.5) {
                if (this.velocity.x < 0) {
                    this.sprite.scale.x = -Math.abs(this.config.scale);
                } else {
                    this.sprite.scale.x = Math.abs(this.config.scale);
                }
            }
        } else {
            // Close enough to target, apply smooth deceleration (horizontal only)
            this.velocity.x *= 0.85; // Smoother friction

            // Stop very small movements to prevent jittering
            if (Math.abs(this.velocity.x) < 0.1) this.velocity.x = 0;
        }

        // Note: velocity.y is not modified here - it's handled by physics
    }

    /**
     * Override animation method to use directional frames
     */
    setAnimation(state: 'idle' | 'walking' | 'running'): void {
        if (this.currentAnimation === state || !this.isInitialized) return;

        this.currentAnimation = state;

        let frames: PIXI.Texture[];

        if (state === 'idle') {
            // Use forward-facing frames for idle (frames 19-24 in 1-based indexing)
            frames = this.frames.idleFacingForward;
        } else {
            // For walking/running, determine direction based on velocity
            const isMovingLeft = this.velocity.x < -0.1;
            const isMovingRight = this.velocity.x > 0.1;

            if (isMovingLeft) {
                frames = this.frames.walkingFacingLeft;
            } else if (isMovingRight) {
                frames = this.frames.walkingFacingRight;
            } else {
                // If not moving horizontally much, use forward-facing walking
                frames = this.frames.walkingFacingForward;
            }
        }

        // Instead of destroying and recreating the sprite, just update its textures
        if (this.sprite && frames && frames.length > 0) {
            console.log('FarmerCompanion.setAnimation: updating textures for', state, 'with', frames.length, 'frames');

            // Stop current animation
            this.sprite.stop();

            // Update the sprite's textures
            this.sprite.textures = frames;

            // Reset to first frame and restart animation
            this.sprite.currentFrame = 0;
            this.sprite.play();
        } else {
            console.log('FarmerCompanion.setAnimation: sprite or frames not available', {
                hasSprite: !!this.sprite,
                framesLength: frames?.length || 0
            });
        }
    }

    /**
     * Farmer-specific animation handling
     */
    protected updateAnimation(): void {
        const speed = Math.sqrt(this.velocity.x * this.velocity.x + this.velocity.y * this.velocity.y);

        // Farmer moves a bit slower than other companions
        if (speed > 0.5) {
            this.setAnimation('walking');
        } else {
            this.setAnimation('idle');
        }
    }
}

/**
 * Factory function to create a farmer companion
 */
export function createFarmerCompanion(app: PIXI.Application): FarmerCompanion {
    return new FarmerCompanion(app);
}
