import * as PIXI from 'pixi.js';
import { BaseCompanion } from '../CompanionManager';
import type { CompanionConfig } from '../../types';

/**
 * Farmer companion configuration
 */
export const FARMER_CONFIG: CompanionConfig = {
    type: 'farmer',
    name: 'Farmer',
    spriteSheet: '/assets/farmer/farmer-idle.png',
    frameCount: 24,
    frameSize: { width: 32, height: 32 },
    scale: 4,
    animationSpeed: 0.08, // Slower animation for more natural idle movement
    followDistance: 80,
    moveSpeed: 3
};

/**
 * Farmer walking sprite configuration
 */
export const FARMER_WALKING_CONFIG = {
    spriteSheet: '/assets/farmer/farmer-walking.png',
    frameCount: 24,
    frameSize: { width: 32, height: 32 }
};

/**
 * Extended frames interface for directional animations
 */
interface FarmerFrames {
    idle: PIXI.Texture[];
    walking: PIXI.Texture[];
    // Idle frames by direction
    idleFacingRight: PIXI.Texture[];
    idleFacingLeft: PIXI.Texture[];
    idleFacingBack: PIXI.Texture[];
    idleFacingForward: PIXI.Texture[];
    // Walking frames by direction
    walkingFacingRight: PIXI.Texture[];
    walkingFacingLeft: PIXI.Texture[];
    walkingFacingBack: PIXI.Texture[];
    walkingFacingForward: PIXI.Texture[];
}

/**
 * Farmer companion class
 */
export class FarmerCompanion extends BaseCompanion {
    protected declare frames: FarmerFrames;

    constructor(app: PIXI.Application) {
        super(app, FARMER_CONFIG);
        // Initialize extended frames structure
        this.frames = {
            idle: [],
            walking: [],
            // Idle frames by direction
            idleFacingRight: [],
            idleFacingLeft: [],
            idleFacingBack: [],
            idleFacingForward: [],
            // Walking frames by direction
            walkingFacingRight: [],
            walkingFacingLeft: [],
            walkingFacingBack: [],
            walkingFacingForward: []
        };
    }

    /**
     * Load farmer sprite sheets and extract frames
     */
    protected async loadSprites(): Promise<void> {
        try {
            // Load both idle and walking sprite sheets
            const [idleTexture, walkingTexture] = await Promise.all([
                PIXI.Assets.load(this.config.spriteSheet),
                PIXI.Assets.load(FARMER_WALKING_CONFIG.spriteSheet)
            ]);

            // Extract frames from both sprite sheets
            // Both sprite sheets are 32x32 pixels per frame, 24 frames total
            const frameWidth = this.config.frameSize.width;
            const frameHeight = this.config.frameSize.height;
            const totalFrames = this.config.frameCount;

            // Helper function to extract frames from a texture
            const extractFrames = (texture: PIXI.Texture): PIXI.Texture[] => {
                const textureWidth = texture.width;
                const framesPerRow = Math.floor(textureWidth / frameWidth);
                const frames: PIXI.Texture[] = [];

                for (let i = 0; i < totalFrames; i++) {
                    const col = i % framesPerRow;
                    const row = Math.floor(i / framesPerRow);

                    const frame = new PIXI.Rectangle(
                        col * frameWidth,
                        row * frameHeight,
                        frameWidth,
                        frameHeight
                    );

                    const frameTexture = new PIXI.Texture({ source: texture.source, frame });
                    frames.push(frameTexture);
                }

                return frames;
            };

            // Extract frames from both textures
            const idleFrames = extractFrames(idleTexture);
            const walkingFrames = extractFrames(walkingTexture);

            // Organize idle frames by direction (6 frames each)
            // Frame layout: [0-5] facing right, [6-11] facing back, [12-17] facing left, [18-23] facing forward
            this.frames.idleFacingRight = idleFrames.slice(0, 6);    // Frames 0-5
            this.frames.idleFacingBack = idleFrames.slice(6, 12);     // Frames 6-11
            this.frames.idleFacingLeft = idleFrames.slice(12, 18);    // Frames 12-17
            this.frames.idleFacingForward = idleFrames.slice(18, 24); // Frames 18-23

            // Organize walking frames by direction (6 frames each)
            this.frames.walkingFacingRight = walkingFrames.slice(0, 6);    // Frames 0-5
            this.frames.walkingFacingBack = walkingFrames.slice(6, 12);     // Frames 6-11
            this.frames.walkingFacingLeft = walkingFrames.slice(12, 18);    // Frames 12-17
            this.frames.walkingFacingForward = walkingFrames.slice(18, 24); // Frames 18-23

            // Set default idle and walking frames (forward-facing)
            this.frames.idle = this.frames.idleFacingForward;
            this.frames.walking = this.frames.walkingFacingForward;

            console.log(`Farmer companion loaded: ${this.frames.idle.length} idle frames, ${this.frames.walking.length} walking frames, 6 frames per direction for each animation`);

        } catch (error) {
            console.error('Failed to load farmer sprites:', error);
            throw error;
        }
    }

    /**
     * Farmer-specific behavior updates
     */
    update(deltaTime: number, targetPosition: { x: number; y: number }): void {
        if (!this.isInitialized) return;

        this.targetPosition = targetPosition;
        this.updateMovement(deltaTime);
        this.updateAnimation(); // This calls our overridden updateAnimation method
        this.updateSpritePosition();

        // Add farmer-specific behaviors here if needed
        // For example: occasional farming animations, tool interactions, etc.
    }

    /**
     * Override animation method to use directional frames
     */
    setAnimation(state: 'idle' | 'walking' | 'running'): void {
        if (this.currentAnimation === state || !this.isInitialized) return;

        console.log(`FarmerCompanion.setAnimation: ${this.currentAnimation} -> ${state}`);
        this.currentAnimation = state;

        let frames: PIXI.Texture[];

        if (state === 'idle') {
            // Use forward-facing frames for idle (frames 19-24 in 1-based indexing)
            frames = this.frames.idleFacingForward;
        } else {
            // For walking/running, determine direction based on velocity
            const isMovingLeft = this.velocity.x < -0.1;
            const isMovingRight = this.velocity.x > 0.1;

            if (isMovingLeft) {
                frames = this.frames.walkingFacingLeft;
            } else if (isMovingRight) {
                frames = this.frames.walkingFacingRight;
            } else {
                // If not moving horizontally much, use forward-facing walking
                frames = this.frames.walkingFacingForward;
            }
        }

        // Instead of destroying and recreating the sprite, just update its textures
        if (this.sprite && frames && frames.length > 0) {
            // Stop current animation
            this.sprite.stop();

            // Update the sprite's textures
            this.sprite.textures = frames;

            // Reset to first frame and restart animation
            this.sprite.currentFrame = 0;
            this.sprite.play();
        }
    }

    /**
     * Farmer-specific animation handling
     */
    protected updateAnimation(): void {
        const speed = Math.sqrt(this.velocity.x * this.velocity.x + this.velocity.y * this.velocity.y);

        // Farmer moves a bit slower than other companions
        if (speed > 0.5) {
            this.setAnimation('walking');
        } else {
            this.setAnimation('idle');
        }
    }
}

/**
 * Factory function to create a farmer companion
 */
export function createFarmerCompanion(app: PIXI.Application): FarmerCompanion {
    return new FarmerCompanion(app);
}
